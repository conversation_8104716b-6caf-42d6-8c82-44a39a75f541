version: '3.8'

services:
  web:
    build:
      context: .
      dockerfile: Dockerfile
    volumes:
      - .:/app
      - ./mediafiles:/gg/mediafiles
    ports:
      - "8000:8000"
    environment:
      - CELERY_BROKER_REDIS_URL=redis://redis:6379/0
      - IMAGEMAGICK_BINARY=/usr/bin/convert
    depends_on:
      - redis
      - imagemagick

  celery:
    build:
      context: .
      dockerfile: Dockerfile.celery
    volumes:
      - .:/app
      - ./mediafiles:/app/mediafiles
    environment:
      - CELERY_BROKER_REDIS_URL=redis://redis:6379/0
      - IMAGEMAGICK_BINARY=/usr/bin/convert
    depends_on:
      - redis
      - imagemagick

  redis:
    image: redis:6
    container_name: myproject-redis

  imagemagick:
    image: dpokidov/imagemagick
    container_name: imagemagick
    entrypoint: ["convert"]  # Optional: specify default command if needed

networks:
  default:
    driver: bridge

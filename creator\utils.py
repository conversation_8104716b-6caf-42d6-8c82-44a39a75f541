from moviepy.editor import (
    VideoFileClip,
    CompositeVideoClip,
    AudioFileClip, 
    ImageClip, 
    clips_array 
    )


from moviepy.video.tools.subtitles import SubtitlesClip
from moviepy.video.VideoClip import TextClip
from elevenlabs import set_api_key
import assemblyai as aai
from pydub import AudioSegment
import subprocess
import random
import tempfile
import logging
import requests
import io
import os
import glob 
from django.conf import settings 
from pytubefix import YouTube
from website.models import PodcastEntry, Account



# Set the path to the ImageMagick binary

API_KEY = '********************************'
set_api_key(API_KEY)
aai.settings.api_key = "********************************"
transcriber = aai.Transcriber() 
media_root = settings.MEDIA_ROOT 



def save_final_video(final_video):
    """
    Saves the final video to a specified output directory.

    :param final_video: The video clip object to be saved.
    :param output_dir: The directory where the final video will be saved. Defaults to settings.DEFAULT_OUTPUT_DIR.
    """
    clip_name = str(count_files_in_folder(str(media_root)+'/creator/category/test/output/')) + '.mp4'
    final_video.write_videofile(str(media_root)+'/creator/category/test/output/'+clip_name, codec='libx265')
    final_video.close()


def count_files_in_folder(folder_path):
    """
    Counts the number of files in a given folder.

    :param folder_path: The path to the folder whose files need to be counted.
    :return: The number of files in the folder, or 0 if an error occurs.
    """
    try:
        files = os.listdir(folder_path)
        file_count = len([f for f in files if os.path.isfile(os.path.join(folder_path, f))])
        return file_count
    except OSError as e:
        logging.error(f"Error counting files in {folder_path}: {e}")
        return 0  # or an appropriate error code

def select_random_video():
    # List all video files in the folder
    videos_path = os.path.join(media_root, 'creator/gameplays')
    video_files = [file for file in os.listdir(videos_path) if file.endswith(('.mp4', '.avi', '.mkv', '.mov'))]
    
    if not video_files:
        print("No video files found in the folder.")
        return None

    # Select a random video file from the list
    random_video_file = random.choice(video_files)

    # Return the full path of the selected video
    return os.path.join(videos_path, random_video_file)



def get_random_clip_from_videos(clip_duration):
    """
    Retrieves a random clip of specified duration from a randomly selected video.

    :param clip_duration: Duration of the clip in seconds.
    :return: A VideoFileClip object of the random clip, or None if an error occurs.
    """
    video_path = select_random_video()
    if not video_path:
        logging.warning("No video found for extracting a clip.")
        return None

    video = VideoFileClip(video_path)
    max_start = max(0, video.duration - clip_duration)
    start_time = random.uniform(0, max_start)
    return video.subclip(start_time, start_time + clip_duration)



def load_logos_and_clips(duration, image_extensions=["*.png", "*.jpg", "*.jpeg"]):
    try:
        images = []
        video_clips = []
        
        img_path = 'creator/category/test/logos'
        pattern = [os.path.join(media_root, img_path, ext) for ext in image_extensions]
        image_files = [file for p in pattern for file in glob.glob(p)]
        
        for image_path in image_files:
            try:
                image_clip = ImageClip(image_path)
                images.append(image_clip)
            except Exception as e:
                logging.error(f"Error loading image: {e}")
        
        for _ in image_files:
            try:
                video_clip = get_random_clip_from_videos(duration)
                video_clips.append(video_clip)
            except Exception as e:
                logging.error(f"Error loading video clip: {e}")

        return images, video_clips
    except Exception as e:
        logging.error(f"Error in load_logos_and_clips: {e}")
        return [], []


def crop_9_16(clip):
    try:
        width, height = clip.size
        if width < 0 or height < 0:
            raise ValueError("Invalid clip dimensions")

        target_width = (9 * height) // 16
        x1 = (width - target_width) // 2
        x2 = x1 + target_width
        return clip.crop(x1=x1, x2=x2, y1=0, y2=height)
    except Exception as e:
        logging.error(f"Error in crop_9_16: {e}")
        return None

def get_audio_clip_from_bytesio(audio_bytesio):
    try:
        with tempfile.NamedTemporaryFile(suffix=".mp3", delete=False) as temp_audio_file:
            temp_audio_file.write(audio_bytesio.getvalue())
            temp_audio_path = temp_audio_file.name
        return AudioFileClip(temp_audio_path)
    except Exception as e:
        logging.error(f"Error creating audio clip: {e}")
        return None


def crop_video(clip, width, height):
        '''
        Crop a video clip to a specified width and height.

        Parameters:
            clip (VideoClip): The original video clip to be cropped.
            width (int): The desired width of the cropped video.
            height (int): The desired height of the cropped video.

        Returns:
            VideoClip: A new video clip that is a cropped version of the original clip.
        '''

        # Get the dimensions of the original video
        original_width, original_height = clip.size

        # Calculate the coordinates for cropping
        x1 = (original_width - width) // 2
        y1 = (original_height - height) // 2
        x2 = x1 + width
        y2 = y1 + height

        # Crop the video
        cropped_clip = clip.crop(x1=x1, y1=y1, x2=x2, y2=y2)

        return cropped_clip

def generate_voiceover_from_text(text_content):

    #folder_path = voiceover_folder
    #filename = "voiceover_elevenlabs.mp3"

    url = "https://api.elevenlabs.io/v1/text-to-speech/DUdKq4ZYPGN7pXABa4qw"
    headers = {
        "Accept": "audio/mpeg",
        "Content-Type": "application/json",
        "xi-api-key": API_KEY
    }
    data = {
        "text": text_content,
        "model_id":"eleven_monolingual_v1",
        "voice_settings": {
            "stability": .5,
            "similarity_boost": 0.6,
        }
    }

    response = requests.post(url, json=data, headers=headers)
    
    response.raise_for_status()
    audio_data = response.content


    speed = 1.0
    # Change speed of audio data
    audio_segment = AudioSegment.from_file(io.BytesIO(audio_data), format="mp3")
    audio_segment = audio_segment._spawn(audio_segment.raw_data, overrides={"frame_rate": int(audio_segment.frame_rate * speed)})



    #with open(os.path.join(voiceover_folder, filename),'wb') as f:
    #    audio_segment.export(f, format='mp3')

    #return audio_segment.export(format='mp3').read()

    audio_clip = get_audio_clip_from_bytesio(io.BytesIO(audio_segment.export(format='mp3').read()))
    return audio_clip, audio_data, audio_segment

def transcribe_text_from_audio_and_save(audio_data_bytes):

    with tempfile.NamedTemporaryFile(suffix='.mp3', delete=False) as temp_audio_file:
        temp_audio_file.write(audio_data_bytes)

    temp_audio_path = temp_audio_file.name
    transcript = transcriber.transcribe(temp_audio_path)


    with tempfile.NamedTemporaryFile(suffix='.srt', delete=False) as temp_srt_file:
        temp_srt_file.write(transcript.export_subtitles_srt(chars_per_caption=17).encode('utf-8'))


    #with open(output_dir, 'w') as f:
        #f.writelines(transcript.export_subtitles_srt(chars_per_caption=15))

    temp_audio_file.close()

    return temp_srt_file.name

def generate_story(text):


    voiceover, voiceover_data, voiceover_segment = generate_voiceover_from_text(text) 
    with tempfile.NamedTemporaryFile(suffix='.mp3', delete=False) as temp_voiceover_file:
        temp_voiceover_file.write(voiceover_data)

    voiceover_duration = voiceover_segment.duration_seconds
    logos, clips = load_logos_and_clips(voiceover_duration)
    srt_file = transcribe_text_from_audio_and_save(voiceover_data)  # uses assembly ai api
    

    def generator(text):
        return TextClip(text, font='DejaVu-Sans-Mono-Bold-Oblique', fontsize=60, color='yellow', stroke_color='black', stroke_width=2, method='label')

    subtitles_clip = SubtitlesClip(srt_file, generator).set_position(('center')).set_duration(voiceover_duration)
    
    for i, logo in enumerate(logos):

        video_clip = clips[i].set_audio(voiceover)
        logo = logo.set_end(1).set_start(1).set_duration(voiceover_duration).set_position('bottom')
        final_clip = CompositeVideoClip([video_clip, subtitles_clip, logo])
        save_final_video(final_clip)

def transcribe_text_from_mp3_and_save(audio_file):
    """
    Transcribes text from an MP3 audio file and saves the transcription as an SRT file.

    Args:
        audio_file (str): Path to the input MP3 audio file.

    Returns:
        str: Path to the generated SRT file.
    """

    with tempfile.NamedTemporaryFile(suffix='.mp3', delete=False) as temp_audio_file:
        audio_file.write_audiofile(temp_audio_file.name, codec='libmp3lame')
        transcript = transcriber.transcribe(temp_audio_file.name)
        with tempfile.NamedTemporaryFile(suffix='.srt', delete=False) as temp_srt_file:
            temp_srt_file.write(transcript.export_subtitles_srt(chars_per_caption=17).encode('utf-8'))
            return temp_srt_file.name
def generate_podcast(url: str):

    # Create a YouTube object
    yt = YouTube(url)

    # Get the highest resolution stream (you can change this based on your preference)
    video_stream = yt.streams.get_highest_resolution()

    # Create temporary files to store the video and subtitles
    with tempfile.NamedTemporaryFile(delete=False, suffix=".mp4") as video_file:
        video_stream.download(output_path='', filename='test')

    subtitle_file = None
    subtitle_stream = yt.streams.filter(file_extension='srt').first()
    if subtitle_stream:
        with tempfile.NamedTemporaryFile(delete=False, suffix=".srt") as subtitle_file:
            subtitle_stream.download(output_path='', filename='test')

    print("Video and subtitles downloaded to temporary files.")
    return video_file, subtitle_file


def check_subtitle_file(file_path):
    # Specify the path to your Go program
    go_program_path = 'subtitle-overlap-fixer'

    # Specify the path to the subtitle file you want to check
    subtitle_file_path = file_path

    try:
        # Run the Go program using subprocess
        result = subprocess.run(
            [go_program_path, subtitle_file_path],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True  # Capture output as text
        )

        if result.returncode == 0:
            # Success: Go program ran without errors
            output_message = result.stdout
            return output_message
        else:
            # Error: Go program encountered an issue
            error_message = result.stderr
            return error_message

    except Exception as e:
        # Handle any exceptions that may occur during subprocess execution
        return  str(e)
    

def load_logos_and_clips(entry: PodcastEntry):
   
    images = []
    video_clips = []
    
    for account in Account.objects.filter(category=entry.podcast.category):
        image_clip = ImageClip(account.logo.path)
        images.append(image_clip)

        video_clip = get_random_clip_from_videos(entry.end_time - entry.start_time)
        video_clips.append(video_clip)
    return images, video_clips

def clip_youtube_and_subtitles(start, end):
    pass

def get_top_clip(entry):
    entry.fetch_source_location()
    top_clip = VideoFileClip(entry.podcast.source_location)
    return top_clip

def get_podcast_entry(entry_id):
    entry = PodcastEntry.objects.get(id=entry_id)
    entry.mark_as_processing()
    return entry

def create_logo_clip(account, top_clip):
    return ImageClip(account.logo.path).set_end(1).set_start(1).set_duration(top_clip.duration).set_position('bottom')

def create_bottom_clip(entry, top_clip_width, top_clip_height):
    bottom_clip = get_random_clip_from_videos(entry.end_time - entry.start_time)
    return crop_video(bottom_clip.set_audio(None), top_clip_width, top_clip_height)

def create_final_clip(top_clip, bottom_clip, subtitles_clip, logo):
    final_clip = clips_array([ [top_clip], [bottom_clip] ])
    if subtitles_clip:
        return CompositeVideoClip([final_clip, subtitles_clip, logo])
    else:
        return CompositeVideoClip([final_clip, logo])


def write_final_clip_to_file(final_clip, entry, account, category_dir):
    filename = os.path.join(category_dir, 'output')+ f"/entry-{entry.id}-{account.id}.mp4"
    final_clip.write_videofile(filename, codec='libx264')
    return '/media/' + filename.split('/mediafiles/')[1]
    

def podcast_split_screen(entry_id):
    entry = get_podcast_entry(entry_id)
    top_clip = get_top_clip(entry).subclip(entry.start_time, entry.end_time)
    top_clip_width, top_clip_height = top_clip.size
    category_dir = os.path.join(media_root, 'creator', 'category', entry.podcast.category.name)

    def generator(text):
        color = random.choice(['White','White', 'Yellow'])
        return TextClip(
            text,
            font='Times-Bold',
            fontsize=42,
            color=color,
            stroke_color='black',
            stroke_width=2,
            method='label',
    )
    subtitles_clip = None

    if entry.transcripe:
        srt_file = transcribe_text_from_mp3_and_save(top_clip.audio)
        entry.transcript = srt_file
        subtitles_clip = SubtitlesClip(srt_file, generator).set_position(('center', 'center'))

    accounts = Account.objects.filter(category=entry.podcast.category)
    locations = []
    for account in accounts:
        logo = create_logo_clip(account, top_clip)
        bottom_clip = create_bottom_clip(entry, top_clip_width, top_clip_height)
        final_clip = create_final_clip(top_clip, bottom_clip, subtitles_clip, logo)
        location = write_final_clip_to_file(final_clip, entry, account, category_dir)
        locations.append(location)
    entry.update_locations(locations)


    

    # logos, clips = load_logos_and_clips(entry)
    # for i, logo in enumerate(logos):
    #     bottom_clip = get_random_clip_from_videos(entry.end_time - entry.start_time)
    #     bottom_clip = crop_video(bottom_clip.set_audio(None), top_clip_width, top_clip_height)
    #     logo = logo.set_end(1).set_start(1).set_duration(top_clip.duration).set_position('bottom')
    #     final_clip = clips_array([ [top_clip], [bottom_clip] ])
    #     final_clip = CompositeVideoClip([final_clip, subtitles_clip, logo])
    #     output_files_count = count_files_in_folder(category_dir+'/output/')
    #     filename = os.path.join(category_dir, 'output')+f"/{entry.podcast.title}-{str(output_files_count)}.mp4"
    #     final_clip.write_videofile(filename, codec='libx264')
    # entry.mark_as_completed()
        

        
def clean_temporary_directory(temp_dir):
    try:
        if os.path.exists(temp_dir):
            for filename in os.listdir(temp_dir):
                file_path = os.path.join(media_root, temp_dir, filename)
                os.remove(file_path)
            os.rmdir(temp_dir)
            print("Temporary files and directory cleaned up.")
    except Exception as e:
        print(f"An error occurred while cleaning up: {e}")


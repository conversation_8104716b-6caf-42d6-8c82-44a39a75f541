from django.db import models
from django.conf import settings
import os

class UserProfile(models.Model):
    username = models.CharField(max_length=50)
    category = models.ForeignKey('Category', on_delete=models.DO_NOTHING, null=True)
    logo = models.ImageField(upload_to='user_logos/', null=True, blank=True)
    created = models.DateTimeField(auto_now_add=True)
    updated = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return self.username

class Category(models.Model):
    name = models.CharField(max_length=100)
    
    class Meta:
        verbose_name = 'Category'  # Singular name
        verbose_name_plural = 'Categories'

    def __str__(self):
        return self.name
    
    def create_directory_structure(self):
        # Define the base directory path using MEDIA_ROOT
        base_dir = os.path.join(settings.MEDIA_ROOT, 'creator', 'category', self.name)

        # Define subdirectories
        subdirectories = ['logos', 'videos', 'output']

        # Check if the base directory exists before creating
        if not os.path.exists(base_dir):
            os.makedirs(base_dir)

        # Create subdirectories
        for subdirectory in subdirectories:
            subdirectory_path = os.path.join(base_dir, subdirectory)
            if not os.path.exists(subdirectory_path):
                os.makedirs(subdirectory_path)

    def save(self, *args, **kwargs):
        # Call the original save method
        super().save(*args, **kwargs)

        # Create the directory structure
        self.create_directory_structure()
    

    
class Story(models.Model):
    title = models.CharField(max_length=200)
    category = models.ForeignKey(Category, on_delete=models.SET_NULL, null=True, blank=True)
    script = models.TextField(blank=True)
    video_url = models.URLField(blank=True)
    created = models.DateTimeField(auto_now_add=True)  # Automatically set when the story is created
    updated = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return self.title
    
    class Meta:
        verbose_name = 'Story'  # Singular name
        verbose_name_plural = 'Stories'
    


# class Vigen():

        
#     def __init__(self):
#         self.ASSEMBLYAI_API_KEY = "********************************"
#         aai.settings.api_key = self.ASSEMBLYAI_API_KEY
#         self.transcriber = aai.Transcriber()
        
#         super(Vigen, self).__init__()

#     def crop_video(self, clip, width, height):
#         '''
#         Crop a video clip to a specified width and height.

#         Parameters:
#             clip (VideoClip): The original video clip to be cropped.
#             width (int): The desired width of the cropped video.
#             height (int): The desired height of the cropped video.

#         Returns:
#             VideoClip: A new video clip that is a cropped version of the original clip.
#         '''

#         # Get the dimensions of the original video
#         original_width, original_height = clip.size

#         # Calculate the coordinates for cropping
#         x1 = (original_width - width) // 2
#         y1 = (original_height - height) // 2
#         x2 = x1 + width
#         y2 = y1 + height

#         # Crop the video
#         cropped_clip = clip.crop(x1=x1, y1=y1, x2=x2, y2=y2)

#         return cropped_clip
    
#     def crop_9_16(self,video_clip):
#         """
#         Crops a video clip to a 9:16 aspect ratio.

#         Args:
#             video_clip (moviepy.editor.VideoFileClip): Input video clip.

#         Returns:
#             moviepy.editor.VideoFileClip: Cropped video clip with a 9:16 aspect ratio.
#         """

#         width, height = video_clip.size

#         target_width = (9*height) // 16
#         target_height = height

#         x1 = (width - target_width) // 2
#         x2 = x1 + target_width
#         y1 = 0
#         y2 = target_height

#         cropped_clip = video_clip.crop(x1=x1, x2=x2, y1=y1, y2=y2)
#         return cropped_clip
    
#     def transcribe_text_from_mp3_and_save(self,audio_file):
#         """
#         Transcribes text from an MP3 audio file and saves the transcription as an SRT file.

#         Args:
#             audio_file (str): Path to the input MP3 audio file.

#         Returns:
#             str: Path to the generated SRT file.
#         """

#         with tempfile.NamedTemporaryFile(suffix='.mp3', delete=False) as temp_audio_file:
#             audio_file.write_audiofile(temp_audio_file.name, codec='libmp3lame')

#         transcript = self.transcriber.transcribe(temp_audio_file.name)

#         with tempfile.NamedTemporaryFile(suffix='.srt', delete=False) as temp_srt_file:
#             temp_srt_file.write(transcript.export_subtitles_srt(chars_per_caption=17).encode('utf-8'))

#         temp_audio_file.close()

#         return temp_srt_file.name
    



# class Podcast(Vigen):
#     def __init__(self, podcast_url, start, end):
#         super().__init__()
#         self.podcast_url = podcast_url
#         self.start = datetime.datetime.strptime(start, '%H:%M:%S')
#         self.end = datetime.datetime.strptime(end, '%H:%M:%S')
        



#     def select_random_video():
#         # List all video files in the folder
#         video_files = [file for file in os.listdir('videos') if file.endswith(('.mp4', '.avi', '.mkv', '.mov'))]

#         if not video_files:
#             print("No video files found in the folder.")
#             return None

#         # Select a random video file from the list
#         random_video_file = random.choice(video_files)
#         random_video_path = os.path.join('videos', random_video_file)

#         return random_video_path


#     def get_random_clip_from_videos(clip_duration):
#         video_path = select_random_video()
#         # Load the video
#         video_clip = VideoFileClip(video_path)

#         # Calculate the valid range for clip start time based on video duration and clip duration
#         max_start_time = video_clip.duration - clip_duration
#         if max_start_time < 0:
#             print(f"The video '{video_path}' is too short for the specified clip duration.")
#             return None

#         # Generate a random start time within the valid range
#         random_start_time = random.uniform(0, max_start_time)

#         # Extract the random clip
#         random_clip = video_clip.subclip(random_start_time, random_start_time + clip_duration)

#         return random_clip

#     def get_logos_for_category(selected_category):
#         # Fetch all UserProfile objects in the selected category with non-empty logos
#         profiles_with_logos = UserProfile.objects.filter(category=selected_category).exclude(logo='')


#         # Initialize a list to store ImageClip objects
#         logo_clips = []

#         for profile in profiles_with_logos:
#             # Create an ImageClip from the user's logo
#             image_clip = ImageClip(profile.logo.path)
#             logo_clips.append(image_clip)

#         return logo_clips    

#     def podcast_split_screen(self, podcast_video, podcast_caption):

#             top_clip = VideoFileClip(podcast_video)
#             top_clip_width, top_clip_height = top_clip.size

#             bottom_clip = VideoFileClip(gameplay_video)
#             bottom_clip = self.crop_video(bottom_clip, top_clip_width, top_clip_height)

#             #bottom_clip = helper.resize_video_and_return_clip(gameplay_video, top_clip_width, top_clip_height)


#             min_duration = top_clip.duration
#             top_clip = top_clip.subclip(0, min_duration)
#             bottom_clip = bottom_clip.subclip(0, min_duration)

#             bottom_clip = bottom_clip.set_audio(None)


#             generator = lambda text: TextClip(
#                 text,
#                 font='Verdana-Bold',
#                 fontsize=35,
#                 color='yellow',
#                 stroke_color='black',
#                 stroke_width=1,
#                 method='label',
#             )

#             '''
#             we are generating substitles from assemblyai, we don't need that, the helper function
#             already downloads the yt video, trims it and downloads the subtitles and also trims
#             the substitles to match the trimmed video

#             i have an error to overlay the downloaded srt file to the final_clip,
#             to be fixed...
#             '''
#             srt_file = self.transcribe_text_from_mp3_and_save(top_clip.audio)
#             print(srt_file)

#             subtitles_clip = SubtitlesClip(srt_file, generator)

#             final_clip = clips_array([ [top_clip], [bottom_clip] ]).set_duration(min_duration)
#             final_clip = CompositeVideoClip([final_clip, subtitles_clip.set_position(('center', 'center'))]).set_duration(min_duration)

#             final_clip = self.crop_9_16(final_clip)

#             print('now writing final clip after all processes...')
#             final_clip.write_videofile(settings.Media_root, codec="libx264")
    
     



#     # class Story(models.Model):
#     #     title = models.CharField(max_length=255, blank=False)
#     #     text = models.TextField(blank=False)
#     #     subtitles = models.TextField(blank=True)
#     #     posted = models.BooleanField(default=False)
#     #     created_at = models.DateTimeField(auto_now_add=True)
#     #     updated_at = models.DateTimeField(auto_now=True)
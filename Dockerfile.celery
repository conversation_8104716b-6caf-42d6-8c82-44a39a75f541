# Use a slim Python 3.10.12 base image
FROM python:3.10.12-slim

# Set the working directory inside the container
WORKDIR /app

# Update package list and install ImageMagick, ffmpeg, and necessary tools
RUN apt-get update && apt-get install -y \
    imagemagick \
    ffmpeg \
    && rm -rf /var/lib/apt/lists/*

# Append a policy allowing necessary operations
RUN echo '<?xml version="1.0" encoding="UTF-8"?>' > /etc/ImageMagick-6/policy.xml \
    && echo '<policymap>' >> /etc/ImageMagick-6/policy.xml \
    && echo '  <policy domain="path" rights="read|write" pattern="@*" />' >> /etc/ImageMagick-6/policy.xml \
    && echo '</policymap>' >> /etc/ImageMagick-6/policy.xml
    
# Install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy the entire project into the container
COPY . .

# Command to run Celery worker with specified concurrency and log level
CMD ["celery", "-A", "gg", "worker", "--loglevel=info", "--concurrency=4"]

import os
import io
import glob
import random
import logging
import tempfile
import requests
import subprocess

from django.conf import settings
from pytubefix import YouTube
from moviepy.editor import (
    VideoFileClip, CompositeVideoClip, AudioFileClip, ImageClip, clips_array
)
from moviepy.video.tools.subtitles import SubtitlesClip
from moviepy.video.VideoClip import TextClip
from elevenlabs import set_api_key
import assemblyai as aai
from pydub import AudioSegment
from website.models import PodcastEntry, Account

# --- Configuration ---
logging.basicConfig(level=logging.INFO)
MEDIA_ROOT = settings.MEDIA_ROOT
ELEVENLABS_API_KEY = os.environ.get('ELEVENLABS_API_KEY')
ASSEMBLYAI_API_KEY = os.environ.get('ASSEMBLYAI_API_KEY')

set_api_key(ELEVENLABS_API_KEY)
aai.settings.api_key = ASSEMBLYAI_API_KEY
transcriber = aai.Transcriber()

# --- Utility Functions ---

def count_files_in_folder(folder_path):
    try:
        files = os.listdir(folder_path)
        return len([f for f in files if os.path.isfile(os.path.join(folder_path, f))])
    except OSError as e:
        logging.error(f"Error counting files in {folder_path}: {e}")
        return 0

def select_random_video():
    videos_path = os.path.join(MEDIA_ROOT, 'creator/gameplays')
    video_files = [file for file in os.listdir(videos_path) if file.endswith(('.mp4', '.avi', '.mkv', '.mov'))]
    if not video_files:
        logging.warning("No video files found in the folder.")
        return None
    return os.path.join(videos_path, random.choice(video_files))

def get_random_clip_from_videos(clip_duration):
    video_path = select_random_video()
    if not video_path:
        return None
    video = VideoFileClip(video_path)
    max_start = max(0, video.duration - clip_duration)
    start_time = random.uniform(0, max_start)
    return video.subclip(start_time, start_time + clip_duration)

def crop_video(clip, width, height):
    original_width, original_height = clip.size
    x1 = (original_width - width) // 2
    y1 = (original_height - height) // 2
    x2 = x1 + width
    y2 = y1 + height
    return clip.crop(x1=x1, y1=y1, x2=x2, y2=y2)

def get_audio_clip_from_bytesio(audio_bytesio):
    with tempfile.NamedTemporaryFile(suffix=".mp3", delete=False) as temp_audio_file:
        temp_audio_file.write(audio_bytesio.getvalue())
        temp_audio_path = temp_audio_file.name
    return AudioFileClip(temp_audio_path)

def generate_voiceover_from_text(text_content):
    url = "https://api.elevenlabs.io/v1/text-to-speech/DUdKq4ZYPGN7pXABa4qw"
    headers = {
        "Accept": "audio/mpeg",
        "Content-Type": "application/json",
        "xi-api-key": ELEVENLABS_API_KEY
    }
    data = {
        "text": text_content,
        "model_id": "eleven_monolingual_v1",
        "voice_settings": {"stability": 0.5, "similarity_boost": 0.6}
    }
    response = requests.post(url, json=data, headers=headers)
    response.raise_for_status()
    audio_data = response.content
    audio_segment = AudioSegment.from_file(io.BytesIO(audio_data), format="mp3")
    audio_clip = get_audio_clip_from_bytesio(io.BytesIO(audio_segment.export(format='mp3').read()))
    return audio_clip, audio_data, audio_segment

def transcribe_audio_to_srt(audio_data_bytes, chars_per_caption=17):
    with tempfile.NamedTemporaryFile(suffix='.mp3', delete=False) as temp_audio_file:
        temp_audio_file.write(audio_data_bytes)
        temp_audio_path = temp_audio_file.name
    transcript = transcriber.transcribe(temp_audio_path)
    with tempfile.NamedTemporaryFile(suffix='.srt', delete=False) as temp_srt_file:
        temp_srt_file.write(transcript.export_subtitles_srt(chars_per_caption=chars_per_caption).encode('utf-8'))
        return temp_srt_file.name

def save_final_video(final_video, output_dir=None):
    if output_dir is None:
        output_dir = os.path.join(MEDIA_ROOT, 'creator/category/test/output/')
    os.makedirs(output_dir, exist_ok=True)
    clip_name = f"{count_files_in_folder(output_dir)}.mp4"
    output_path = os.path.join(output_dir, clip_name)
    logging.info(f"Saving final video to {output_path}")
    final_video.write_videofile(output_path, codec='libx265')
    final_video.close()
    return output_path

def load_logos_and_clips(duration, image_extensions=["*.png", "*.jpg", "*.jpeg"]):
    images, video_clips = [], []
    img_path = os.path.join(MEDIA_ROOT, 'creator/category/test/logos')
    image_files = []
    for ext in image_extensions:
        image_files.extend(glob.glob(os.path.join(img_path, ext)))
    for image_path in image_files:
        try:
            images.append(ImageClip(image_path))
            video_clips.append(get_random_clip_from_videos(duration))
        except Exception as e:
            logging.error(f"Error loading image or video: {e}")
    return images, video_clips

def generate_story(text):
    voiceover, voiceover_data, voiceover_segment = generate_voiceover_from_text(text)
    voiceover_duration = voiceover_segment.duration_seconds
    logos, clips = load_logos_and_clips(voiceover_duration)
    srt_file = transcribe_audio_to_srt(voiceover_data)
    def generator(txt):
        return TextClip(txt, font='DejaVu-Sans-Mono-Bold-Oblique', fontsize=60, color='yellow', stroke_color='black', stroke_width=2, method='label')
    subtitles_clip = SubtitlesClip(srt_file, generator).set_position('center').set_duration(voiceover_duration)
    for i, logo in enumerate(logos):
        video_clip = clips[i].set_audio(voiceover)
        logo_clip = logo.set_end(1).set_start(1).set_duration(voiceover_duration).set_position('bottom')
        final_clip = CompositeVideoClip([video_clip, subtitles_clip, logo_clip])
        save_final_video(final_clip)

def generate_podcast(url: str):
    yt = YouTube(url)
    video_stream = yt.streams.get_highest_resolution()
    with tempfile.NamedTemporaryFile(delete=False, suffix=".mp4") as video_file:
        video_stream.download(filename=video_file.name)
    subtitle_file = None
    subtitle_stream = yt.streams.filter(file_extension='srt').first()
    if subtitle_stream:
        with tempfile.NamedTemporaryFile(delete=False, suffix=".srt") as subtitle_file:
            subtitle_stream.download(filename=subtitle_file.name)
    logging.info("Video and subtitles downloaded to temporary files.")
    return video_file.name, subtitle_file.name if subtitle_file else None

def check_subtitle_file(file_path):
    go_program_path = 'subtitle-overlap-fixer'
    try:
        result = subprocess.run(
            [go_program_path, file_path],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        if result.returncode == 0:
            return result.stdout
        else:
            return result.stderr
    except Exception as e:
        return str(e)

def clean_temporary_directory(temp_dir):
    try:
        if os.path.exists(temp_dir):
            for filename in os.listdir(temp_dir):
                file_path = os.path.join(temp_dir, filename)
                os.remove(file_path)
            os.rmdir(temp_dir)
            logging.info("Temporary files and directory cleaned up.")
    except Exception as e:
        logging.error(f"An error occurred while cleaning up: {e}")

# --- Podcast Split Screen Example ---

def podcast_split_screen(entry_id):
    entry = PodcastEntry.objects.get(id=entry_id)
    entry.mark_as_processing()
    top_clip = VideoFileClip(entry.podcast.source_location).subclip(entry.start_time, entry.end_time)
    top_clip_width, top_clip_height = top_clip.size
    category_dir = os.path.join(MEDIA_ROOT, 'creator', 'category', entry.podcast.category.name)
    def generator(text):
        color = random.choice(['White', 'Yellow'])
        return TextClip(
            text,
            font='Times-Bold',
            fontsize=42,
            color=color,
            stroke_color='black',
            stroke_width=2,
            method='label',
        )
    subtitles_clip = None
    if entry.transcripe:
        srt_file = transcribe_audio_to_srt(top_clip.audio.to_soundarray().tobytes())
        entry.transcript = srt_file
        subtitles_clip = SubtitlesClip(srt_file, generator).set_position(('center', 'center'))
    accounts = Account.objects.filter(category=entry.podcast.category)
    locations = []
    for account in accounts:
        logo = ImageClip(account.logo.path).set_end(1).set_start(1).set_duration(top_clip.duration).set_position('bottom')
        bottom_clip = get_random_clip_from_videos(entry.end_time - entry.start_time)
        bottom_clip = crop_video(bottom_clip.set_audio(None), top_clip_width, top_clip_height)
        final_clip = clips_array([[top_clip], [bottom_clip]])
        if subtitles_clip:
            final_clip = CompositeVideoClip([final_clip, subtitles_clip, logo])
        else:
            final_clip = CompositeVideoClip([final_clip, logo])
        filename = os.path.join(category_dir, 'output', f"entry-{entry.id}-{account.id}.mp4")
        os.makedirs(os.path.dirname(filename), exist_ok=True)
        final_clip.write_videofile(filename, codec='libx264')
        locations.append('/media/' + filename.split('/mediafiles/')[1])
    entry.update_locations(locations)